import request from '@/util/request'
import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_Inventory'

export function GetPageList(data) {
    const api = '/api/CallMaterialSheet/GetPageList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function SearchLines(data) {
    const api = '/api/LineAndEquipmentView/GetLineList'
    return getRequestResources(baseURL, api, 'post', data);
}

export function SearchOrders(data) {
    const api = '/api/CallMaterialSheet/GetProductionOrderListByLine'
    return getRequestResources(baseURL, api, 'get', data);
}

export function GetMaterialsByOrder(data) {
    const api = '/api/CallMaterialSheet/GetCallMaterialDetailsByOrder'
    return getRequestResources(baseURL, api, 'get', data);
}

export function AddCallMaterialSheet(data) {
    const api = '/api/CallMaterialSheet/AddCallMaterialSheet'
    return getRequestResources(baseURL, api, 'post', data);
}

export function UpdateCallMaterialSheet(data) {
    const api = '/api/CallMaterialSheet/UpdateCallMaterialSheet'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetCallMaterialSheetById(data) {
    const api = '/api/CallMaterialSheet/GetCallMaterialSheetById'
    return getRequestResources(baseURL, api, 'post', data);
}

export function GetDistributionDetails(data) {
    const api = '/api/DistributionMaterialRequest/GetPageListWithDetails'
    return getRequestResources(baseURL, api, 'post', data);
}