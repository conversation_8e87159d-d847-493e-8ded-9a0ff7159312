<template>
  <div class="usemystyle MaterialManagement">
    <!-- 搜索区域 -->
    <div class="InventorySearchBox">
      <div class="searchbox">
        <div class="datebox">
          <div class="datepickbox">
            <el-date-picker
              v-model="timepicker"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="-"
              :start-placeholder="$t('DFM_RL._KSRQ')"
              :end-placeholder="$t('DFM_RL._JSRQ')"
            ></el-date-picker>
          </div>
        </div>
        <div class="extrabtn">
          <el-button
            size="mini"
            @click="showDateBox = !showDateBox"
            icon="el-icon-s-tools"
          ></el-button>
        </div>
      </div>
      <div class="searchbox" v-if="showDateBox">
        <div class="inputformbox" v-for="(item, index) in searchlist" :key="index">
          <el-input 
            v-if="item.type == 'input'" 
            v-model="item.value" 
            :myid="item.id" 
            :placeholder="item.name">
          </el-input>
        </div>
      </div>
      <div class="searchbox">
        <el-button style="margin-left: 5px" size="small" icon="el-icon-search" @click="getsearch()">
          {{ $t('GLOBAL._CX') }}
        </el-button>
        <el-button size="small" style="margin-left: 5px" icon="el-icon-refresh" @click="getempty()">
          {{ $t('GLOBAL._CZ') }}
        </el-button>
        <el-button 
          v-if="ShowADD" 
          class="tablebtn" 
          size="small" 
          style="margin-left: 5px" 
          icon="el-icon-circle-plus-outline" 
          @click="addNew()">
          {{ $t('GLOBAL._XZ') }}
        </el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="tablebox">
      <el-table
        :data="desserts"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        border
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column v-for="(item, index) in headers" :key="index" :prop="item.value" :label="$t(item.text)" :width="item.width" :fixed="item.fixed">
          <template slot-scope="scope">
            <span v-if="scope.column.property == 'RequestTime'">{{ scope.row.RequestTime | formatDate }}</span>
            <span v-else-if="scope.column.property == 'CallStatus'">{{ formatCallStatus(scope.row.CallStatus) }}</span>
            <span v-else-if="scope.column.property == 'ProductionOrder'">{{ scope.row.ProductionOrder ? scope.row.ProductionOrder.productionOrderNo : '' }}</span>
            <span v-else>{{ scope.row[item.value] }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('GLOBAL._ACTIONS')" width="160" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleEdit(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
            <el-button size="mini" type="text" @click="handleViewDetails(scope.row)">{{ $t('GLOBAL._CK') }}</el-button>
            <el-button size="mini" type="text" @click="handleDelete(scope.row)" :disabled="scope.row.CallStatus !== '0'">{{ $t('GLOBAL._SC') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <div class="paginationbox">
      <el-pagination 
        background 
        :current-page="pageOptions.page" 
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageOptions.pageSize"
        layout="->, total, sizes, prev, pager, next, jumper"
        :total="pageOptions.total" 
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" 
      />
    </div>

    <!-- 新增/编辑叫料单对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogFormVisible" 
      width="70%" 
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-form :model="form" :rules="rules" ref="form" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('callMaterialSheet.productionLine')" prop="lineId">
              <el-select
                v-model="form.lineId"
                :placeholder="$t('callMaterialSheet.selectLine')"
                filterable
                remote
                :remote-method="searchLines"
                :loading="lineLoading"
                @change="handleLineChange"
                clearable
                style="width: 100%;"
              >
                <el-option
                  v-for="line in lineOptions"
                  :key="line.ID"
                  :label="`${line.EquipmentCode} ${line.EquipmentName}`"
                  :value="line.ID"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('callMaterialSheet.productionOrder')" prop="productionOrderId">
              <el-select
                v-model="form.productionOrderId"
                :placeholder="$t('callMaterialSheet.selectOrder')"
                filterable
                remote
                :remote-method="searchOrders"
                :loading="orderLoading"
                @change="handleOrderChange"
                clearable
                :disabled="!form.lineId"
                style="width: 100%;"
              >
                <el-option
                  v-for="order in orderOptions"
                  :key="order.ID"
                  :label="order.ProductionOrderNo"
                  :value="order.ID"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('callMaterialSheet.caller')" prop="callerId">
              <el-input v-model="form.callerId" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('callMaterialSheet.callPoint')">
              <el-input v-model="form.callPoint" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('callMaterialSheet.lineSideWarehouse')">
              <el-input v-model="form.lineSideWarehouse" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('GLOBAL._BZ')">
              <el-input v-model="form.remark" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 叫料单预览列表 -->
      <div class="call-material-sheets-section">
        <h4>{{ $t('callMaterialSheet.materialDetails') }}</h4>

        <!-- 操作栏 -->
        <div v-if="callMaterialSheets.length > 0" class="sheets-operations">
          <el-checkbox
            v-model="selectAllSheets"
            @change="handleSelectAllSheets"
          >
            全选
          </el-checkbox>
          <span class="selected-info">
            已选择 {{ selectedSheetsCount }} / {{ callMaterialSheets.length }} 个叫料单
          </span>
        </div>

        <!-- 叫料单列表 -->
        <div
          v-for="(sheet, sheetIndex) in callMaterialSheets"
          :key="sheet.sheetId"
          class="call-sheet-block"
          :class="{ 'selected': sheet.isSelected }"
        >
          <!-- 叫料单头部 -->
          <div class="sheet-header">
            <el-checkbox
              v-model="sheet.isSelected"
            />
            <span class="sheet-title">
              线边仓: {{ sheet.warehouseCode }}
            </span>
            <span class="material-count">
              ({{ sheet.materials.length }} 个物料)
            </span>
            <el-button
              type="text"
              @click="sheet.isExpanded = !sheet.isExpanded"
              :icon="sheet.isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
            >
              {{ sheet.isExpanded ? '收起' : '展开' }}
            </el-button>
          </div>

          <!-- 叫料单配置 -->
          <div v-show="sheet.isExpanded" class="sheet-config">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="叫料点" label-width="80px">
                  <el-input
                    v-model="sheet.sheetConfig.callPoint"
                    size="mini"
                    :placeholder="form.callPoint"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="线边仓" label-width="80px">
                  <el-input
                    v-model="sheet.sheetConfig.lineSideWarehouse"
                    size="mini"
                    :placeholder="form.lineSideWarehouse"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="备注" label-width="80px">
                  <el-input
                    v-model="sheet.sheetConfig.remark"
                    size="mini"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 物料明细表格 -->
          <div v-show="sheet.isExpanded" class="sheet-materials">
            <div style="margin-bottom: 10px; font-size: 12px; color: #666;">
              调试信息: 物料数量 {{ sheet.materials.length }}, 数据: {{ JSON.stringify(sheet.materials[0] || {}) }}
            </div>
            <el-table
              :data="sheet.materials"
              border
              size="mini"
              style="width: 100%"
              max-height="250"
            >
              <el-table-column
                prop="MaterialCode"
                :label="$t('material.code')"
                width="120"
              >
                <template slot-scope="scope">
                  {{ scope.row.MaterialCode || scope.row.materialCode || '无数据' }}
                </template>
              </el-table-column>
              <el-table-column
                prop="MaterialName"
                :label="$t('material.name')"
                width="150"
              >
                <template slot-scope="scope">
                  {{ scope.row.MaterialName || scope.row.materialName || '无数据' }}
                </template>
              </el-table-column>
              <el-table-column
                prop="RequestQty"
                :label="$t('material.requiredQuantity')"
                width="100"
              >
                <template slot-scope="scope">
                  {{ scope.row.RequestQty || scope.row.quantity || scope.row.Quantity || '0' }}
                </template>
              </el-table-column>
              <el-table-column
                prop="Unit"
                :label="$t('material.unit')"
                width="80"
              >
                <template slot-scope="scope">
                  {{ scope.row.Unit || scope.row.unit || '无数据' }}
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('material.actualQuantity')"
                width="120"
              >
                <template slot-scope="scope">
                  <el-input-number
                    v-model="scope.row.ActualQty"
                    :min="0"
                    :max="scope.row.RequestQty"
                    :precision="2"
                    size="mini"
                    style="width: 100%"
                    controls-position="right"
                  />
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('material.batchNo')"
                width="120"
              >
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.BatchNo"
                    size="mini"
                    :placeholder="$t('material.batchNo')"
                  />
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('material.palletNo')"
                width="120"
              >
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.PalletNo"
                    size="mini"
                    :placeholder="$t('material.palletNo')"
                  />
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('GLOBAL._BZ')"
                width="150"
              >
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.Remark"
                    size="mini"
                    :placeholder="$t('GLOBAL._BZ')"
                  />
                </template>
              </el-table-column>
              <el-table-column
                :label="$t('GLOBAL._ACTIONS')"
                width="80"
                fixed="right"
              >
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDeleteMaterial(sheetIndex, scope.$index)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="callMaterialSheets.length === 0" class="empty-state">
          <p>请先选择工单以获取叫料单预览</p>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          {{ $t('GLOBAL._QX') }}
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
          :disabled="!hasValidMaterials"
        >
          {{ $t('GLOBAL._BC') }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看叫料单详情对话框 -->
    <el-dialog 
      :title="$t('callMaterialSheet.viewDetails')" 
      :visible.sync="detailDialogVisible" 
      width="70%"
    >
      <el-form :model="detailForm" label-width="120px" disabled>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('callMaterialSheet.callOrderNo')">
              <el-input v-model="detailForm.RequestSheetNo" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('callMaterialSheet.productionOrder')">
              <el-input v-model="detailForm.ProductionOrderNo" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('callMaterialSheet.caller')">
              <el-input v-model="detailForm.CreateUserId" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('callMaterialSheet.callTime')">
              <el-date-picker
                v-model="detailForm.RequestTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                :placeholder="$t('callMaterialSheet.selectDateTime')"
                disabled
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('callMaterialSheet.callPoint')">
              <el-input v-model="detailForm.PositionCode" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('callMaterialSheet.lineSideWarehouse')">
              <el-input v-model="detailForm.LineCode" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('GLOBAL._BZ')">
              <el-input v-model="detailForm.Remark" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 子表明细 -->
      <div class="detail-section">
        <h4>{{ $t('callMaterialSheet.materialDetails') }}</h4>
        <el-table 
          :data="detailMaterialDetails" 
          border
          style="width: 100%"
          max-height="300"
        >
          <el-table-column 
            prop="MaterialCode" 
            :label="$t('material.code')" 
            width="120"
          />
          <el-table-column 
            prop="MaterialName" 
            :label="$t('material.name')" 
            width="150"
          />
          <el-table-column 
            prop="MaterialVersionCode" 
            :label="$t('material.specification')" 
            width="120"
          />
          <el-table-column 
            prop="Unit" 
            :label="$t('material.unit')" 
            width="80"
          />
          <el-table-column 
            prop="RequestQty" 
            :label="$t('material.requiredQuantity')" 
            width="100"
          />
          <el-table-column 
            prop="ActualQty" 
            :label="$t('material.actualQuantity')" 
            width="100"
          />
          <el-table-column 
            prop="BatchNo" 
            :label="$t('material.batchNo')" 
            width="120"
          />
          <el-table-column 
            prop="PalletNo" 
            :label="$t('material.palletNo')" 
            width="120"
          />
          <el-table-column 
            prop="Remark" 
            :label="$t('GLOBAL._BZ')" 
            width="150"
          />
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">
          {{ $t('GLOBAL._GB') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {GetPageList,SearchOrders,GetCallMaterialSheetById,GetCallMaterialPreviewsByOrder,AddCallMaterialSheetBatch} from '@/api/Inventory/callMaterialSheet';
import { getLineList } from '@/api/factoryPlant/PromatWarehouseMapping.js';
import { CallMaterialSheetColumn } from '@/columns/factoryPlant/callMaterialSheet';
import '@/views/Inventory/mystyle.scss';
import { Message, MessageBox } from 'element-ui';
import moment from 'moment';

export default {
  name: 'CallMaterialSheet',
  props: {
    ShowBtn: {
      type: Boolean,
      default: true
    },
    ShowADD: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      timepicker: [moment().add(-7, 'days').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],
      showDateBox: false,
      searchForm: {
        page: 1,
        rows: 20
      },
      searchlist: [
        {
          name: this.$t('callMaterialSheet.workOrder'),
          id: 'workOrder',
          value: '',
          type: 'input'
        },
        {
          name: this.$t('callMaterialSheet.callOrderNo'),
          id: 'callOrderNo',
          value: '',
          type: 'input'
        },
        {
          name: this.$t('callMaterialSheet.callerId'),
          id: 'callerId',
          value: '',
          type: 'input'
        },
        {
          name: this.$t('callMaterialSheet.callPoint'),
          id: 'callPoint',
          value: '',
          type: 'input'
        }
      ],
      desserts: [],
      headers: CallMaterialSheetColumn,
      loading: false,
      pageOptions: {
        page: 1,
        pageSize: 20,
        total: 0
      },
      multipleSelection: [],
      dialogFormVisible: false,
      detailDialogVisible: false,
      form: {
        lineId: '',
        productionOrderId: '',
        callerId: '',
        RequestTime: '',
        LineCode: '',
        PositionCode: '',
        Remark: ''
      },
      detailForm: {
        ID: '',
        RequestSheetNo: '',
        ProductionOrderId: '',
        ProductionOrderNo: '',
        CreateUserId: '',
        RequestTime: '',
        LineCode: '',
        PositionCode: '',
        CallStatus: '',
        Remark: ''
      },
      rules: {
        lineId: [
          { required: true, message: this.$t('callMaterialSheet.selectLineRequired'), trigger: 'change' }
        ],
        productionOrderId: [
          { required: true, message: this.$t('callMaterialSheet.selectOrderRequired'), trigger: 'change' }
        ]
      },
      materialDetails: [],
      detailMaterialDetails: [],
      currentHeaderId: '',
      isEdit: false,
      dialogTitle: '',
      submitLoading: false,
      lineLoading: false,
      orderLoading: false,
      lineOptions: [],
      orderOptions: [],
      // 新增：叫料单预览列表
      callMaterialSheets: []
    };
  },

  filters: {
    formatDate(value) {
      if (!value) return '';
      return moment(value).format('YYYY-MM-DD HH:mm:ss');
    }
  },

  computed: {
    selectedSheetsCount() {
      return this.callMaterialSheets.filter(sheet => sheet.isSelected).length;
    },

    selectAllSheets: {
      get() {
        return this.callMaterialSheets.length > 0 &&
               this.callMaterialSheets.every(sheet => sheet.isSelected);
      },
      set(value) {
        this.callMaterialSheets.forEach(sheet => {
          sheet.isSelected = value;
        });
      }
    },

    hasValidMaterials() {
      return this.callMaterialSheets.some(sheet =>
        sheet.isSelected && sheet.materials.some(item => item.ActualQty > 0)
      );
    }
  },
  mounted() {
    this.gettabeldata();
  },
  methods: {
    getmainH() {},
    
    getsearch() {
      this.pageOptions.page = 1;
      this.gettabeldata();
    },
    
    getempty() {
      this.timepicker = [moment().add(-7, 'days').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')];
      this.pageOptions.page = 1;
      this.searchlist.forEach(item => {
        item.value = '';
      });
      this.gettabeldata();
    },
    
    async gettabeldata() {
      this.loading = true;
      try {
        const params = {
          pageIndex: this.pageOptions.page,
          pageSize: this.pageOptions.pageSize,
          workOrder: this.searchlist[0].value,
          callOrderNo: this.searchlist[1].value,
          callerId: this.searchlist[2].value,
          callPoint: this.searchlist[3].value,
          startTime: this.timepicker ? this.timepicker[0] : '',
          endTime: this.timepicker ? this.timepicker[1] : ''
        };
        
        const res = await GetPageList(params);
        if (res.success) {
          this.desserts = res.response.data;
          this.pageOptions.total = res.response.dataCount;
        }
      } catch (error) {
        console.error(error);
        Message({
          message: this.$t('GLOBAL._CXYC'),
          type: 'error'
        });
      } finally {
        this.loading = false;
      }
    },
    
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    
    handleSizeChange(val) {
      this.pageOptions.pageSize = val;
      this.gettabeldata();
    },
    
    handleCurrentChange(val) {
      this.pageOptions.page = val;
      this.gettabeldata();
    },
    
    formatCallStatus(status) {
      // 0-新建, 1-叫料中, 2-叫料完成, 3-取消叫料, 4-其他状态
      const statusMap = {
        '0': this.$t('callMaterialSheet.status.new'),
        '1': this.$t('callMaterialSheet.status.calling'),
        '2': this.$t('callMaterialSheet.status.completed'),
        '3': this.$t('callMaterialSheet.status.cancelled'),
        '4': this.$t('callMaterialSheet.status.completed') // 根据接口返回数据，4可能是完成状态
      };
      return statusMap[status] || status;
    },
    
    addNew() {
      this.isEdit = false;
      this.dialogTitle = this.$t('callMaterialSheet.addTitle');
      this.form = {
        lineId: '',
        productionOrderId: '',
        callerId: '', // 后台自动获取
        RequestTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        LineCode: '',
        PositionCode: '',
        Remark: ''
      };
      this.materialDetails = [];
      this.lineOptions = [];
      this.orderOptions = [];
      // 获取产线下拉选项
      this.loadLineOptions();
      this.dialogFormVisible = true;
    },
    
    handleEdit(row) {
      this.isEdit = true;
      this.dialogTitle = this.$t('callMaterialSheet.editTitle');
      // 编辑功能暂不实现，因为接口文档中未提供相关接口
      Message({
        message: this.$t('callMaterialSheet.editNotSupported'),
        type: 'warning'
      });
    },
    
    handleDelete(row) {
      MessageBox.confirm(this.$t('GLOBAL._SCTIPS'), this.$t('GLOBAL._TS'), {
        confirmButtonText: this.$t('GLOBAL._QD'),
        cancelButtonText: this.$t('GLOBAL._GB'),
        type: 'warning'
      }).then(async () => {
        try {
          // 删除接口未在文档中提供，此处暂不实现
          Message({
            message: this.$t('callMaterialSheet.deleteNotSupported'),
            type: 'warning'
          });
        } catch (error) {
          console.error(error);
          Message({
            message: this.$t('GLOBAL._SCCG'),
            type: 'error'
          });
        }
      });
    },
    
    handleViewDetails(row) {
      this.loadDetails(row.ID);
    },
    
    async loadDetails(id) {
      try {
        const res = await GetCallMaterialSheetById({id: id});
        if (res.success) {
          this.detailForm = res.response;
          this.detailMaterialDetails = res.response.Details || [];
          this.detailDialogVisible = true;
        }
      } catch (error) {
        console.error(error);
        Message({
          message: this.$t('GLOBAL._CXYC'),
          type: 'error'
        });
      }
    },
    
    // 搜索产线
    async searchLines(keyword) {
      if (!keyword) return;
      this.lineLoading = true;
      try {
        // 使用与物料仓库产线关系映射页面相同的接口
        const response = await getLineList();
        // 过滤匹配关键词的产线
        this.lineOptions = response.response.filter(item => 
          item.EquipmentCode.includes(keyword) || item.EquipmentName.includes(keyword)
        );
      } catch (error) {
        console.error('搜索产线失败', error);
        Message({
          message: this.$t('callMaterialSheet.searchLineFailed'),
          type: 'error'
        });
      } finally {
        this.lineLoading = false;
      }
    },
    
    // 产线变化
    async handleLineChange(lineId) {
      this.form.productionOrderId = '';
      this.orderOptions = [];
      this.materialDetails = [];
      
      if (!lineId) return;
      
      try {
        this.orderLoading = true;
        // 根据选中的产线ID查找对应的产线信息
        const selectedLine = this.lineOptions.find(item => item.ID === lineId);
        if (selectedLine) {
          // 使用产线编码和工单状态2来获取工单列表
          const response = await SearchOrders({
            lineCode: selectedLine.EquipmentCode
          });
          this.orderOptions = response.response || [];
        }
      } catch (error) {
        console.error('获取工单列表失败', error);
        Message({
          message: this.$t('callMaterialSheet.searchOrderFailed'),
          type: 'error'
        });
      } finally {
        this.orderLoading = false;
      }
    },
    
    // 搜索工单
    async searchOrders(keyword) {
      if (!this.form.lineId || !keyword) return;
      this.orderLoading = true;
      try {
        const response = await SearchOrders({
          lineId: this.form.lineId,
          keyword
        });
        this.orderOptions = response.data || [];
      } catch (error) {
        console.error('搜索工单失败', error);
        Message({
          message: this.$t('callMaterialSheet.searchOrderFailed'),
          type: 'error'
        });
      } finally {
        this.orderLoading = false;
      }
    },
    
    // 工单变化
    async handleOrderChange(orderId) {
      if (!orderId) {
        this.callMaterialSheets = [];
        this.materialDetails = [];
        return;
      }

      try {
        const response = await GetCallMaterialPreviewsByOrder({productionOrderId: orderId});
        debugger;
        console.log('预览接口返回数据:', response);
        if (response.success) {
          const responseData = response.response || [];
          console.log('接口返回的数据数组:', responseData);
          console.log('数据数组长度:', responseData.length);

          if (responseData.length > 0) {
            console.log('第一条数据示例:', responseData[0]);
            console.log('第一条数据的所有字段:', Object.keys(responseData[0]));
          }

          // 按线边仓编码分组处理预览数据
          const groupedByWarehouse = {};

          responseData.forEach((item, index) => {
            console.log(`处理第${index + 1}条数据:`, item);
            console.log(`第${index + 1}条数据的所有字段名:`, Object.keys(item));
            console.log(`第${index + 1}条数据的所有字段值:`, Object.values(item));

            // 尝试多种可能的线边仓字段名
            const warehouseKey = item.LineSideWarehouse ||
                                item.lineSideWarehouse ||
                                item.LineCode ||
                                item.lineCode ||
                                item.warehouseCode ||
                                `DEFAULT_WAREHOUSE_${index}`;

            console.log(`第${index + 1}条数据的线边仓编码:`, warehouseKey);

            if (!groupedByWarehouse[warehouseKey]) {
              groupedByWarehouse[warehouseKey] = [];
            }

            // 动态字段映射 - 根据实际字段名进行映射
            const itemKeys = Object.keys(item);
            console.log('开始动态字段映射，可用字段:', itemKeys);

            // 查找物料编码字段
            const materialCodeField = itemKeys.find(key =>
              key.toLowerCase().includes('materialcode') ||
              key.toLowerCase().includes('material_code') ||
              key.toLowerCase().includes('code')
            );

            // 查找物料名称字段
            const materialNameField = itemKeys.find(key =>
              key.toLowerCase().includes('materialname') ||
              key.toLowerCase().includes('material_name') ||
              key.toLowerCase().includes('name')
            );

            // 查找数量字段
            const quantityField = itemKeys.find(key =>
              key.toLowerCase().includes('quantity') ||
              key.toLowerCase().includes('qty') ||
              key.toLowerCase().includes('amount')
            );

            // 查找单位字段
            const unitField = itemKeys.find(key =>
              key.toLowerCase().includes('unit') ||
              key.toLowerCase().includes('uom')
            );

            console.log('找到的字段映射:');
            console.log('物料编码字段:', materialCodeField, '值:', item[materialCodeField]);
            console.log('物料名称字段:', materialNameField, '值:', item[materialNameField]);
            console.log('数量字段:', quantityField, '值:', item[quantityField]);
            console.log('单位字段:', unitField, '值:', item[unitField]);

            const materialItem = {
              MaterialId: item.MaterialId || item.materialId || '',
              MaterialCode: item[materialCodeField] || '',
              MaterialName: item[materialNameField] || '',
              MaterialVersionId: item.MaterialVersionId || item.materialVersionId || '',
              MaterialVersionCode: item.MaterialVersionCode || item.materialVersionCode || '',
              Unit: item[unitField] || '',
              RequestQty: item[quantityField] || 0,
              // UI编辑字段
              ActualQty: item[quantityField] || 0,
              BatchNo: '',
              PalletNo: '',
              Remark: ''
            };

            console.log('最终映射结果:', materialItem);

            console.log(`第${index + 1}条数据映射后:`, materialItem);
            groupedByWarehouse[warehouseKey].push(materialItem);
          });

          console.log('按线边仓分组后的数据:', groupedByWarehouse);

          // 转换为UI数据结构
          if (Object.keys(groupedByWarehouse).length === 0) {
            console.log('警告：分组后没有数据，可能是线边仓字段名不匹配');
            // 临时方案：创建一个默认分组包含所有数据
            const allMaterials = responseData.map((item) => {
              console.log('默认分组映射 - 原始数据:', item);
              const mapped = {
                MaterialId: item.MaterialId || item.materialId || '',
                MaterialCode: item.MaterialCode || item.materialCode || '',
                MaterialName: item.MaterialName || item.materialName || '',
                MaterialVersionId: item.MaterialVersionId || item.materialVersionId || '',
                MaterialVersionCode: item.MaterialVersionCode || item.materialVersionCode || '',
                Unit: item.Unit || item.unit || '',
                RequestQty: item.Quantity || item.quantity || item.RequestQty || 0,
                ActualQty: item.Quantity || item.quantity || item.RequestQty || 0,
                BatchNo: '',
                PalletNo: '',
                Remark: ''
              };
              console.log('默认分组映射 - 映射结果:', mapped);
              return mapped;
            });

            // 测试：同时创建映射版本和原始版本
            this.callMaterialSheets = [
              {
                sheetId: 'MAPPED_SHEET_1',
                warehouseCode: 'MAPPED_DATA',
                materials: allMaterials,
                isExpanded: true,
                isSelected: true,
                sheetConfig: {
                  callPoint: this.form.callPoint || '',
                  lineSideWarehouse: 'MAPPED_DATA',
                  remark: '',
                  requestType: '1',
                  callMaterialType: 'NORMAL'
                }
              },
              {
                sheetId: 'RAW_SHEET_1',
                warehouseCode: 'RAW_DATA',
                materials: responseData, // 直接使用原始数据
                isExpanded: true,
                isSelected: true,
                sheetConfig: {
                  callPoint: this.form.callPoint || '',
                  lineSideWarehouse: 'RAW_DATA',
                  remark: '',
                  requestType: '1',
                  callMaterialType: 'NORMAL'
                }
              }
            ];
          } else {
            this.callMaterialSheets = Object.keys(groupedByWarehouse).map((warehouseCode, index) => ({
              sheetId: `SHEET_${warehouseCode}_${index + 1}`, // 生成临时叫料单ID
              warehouseCode: warehouseCode, // 保存线边仓编码
              materials: groupedByWarehouse[warehouseCode],
              isExpanded: true,
              isSelected: true,
              sheetConfig: {
                callPoint: this.form.callPoint || '',
                lineSideWarehouse: warehouseCode, // 使用分组的线边仓编码
                remark: '',
                requestType: '1',
                callMaterialType: 'NORMAL'
              }
            }));
          }

          console.log('生成的叫料单列表:', this.callMaterialSheets);

          // 为了兼容现有逻辑，同时更新materialDetails
          this.materialDetails = this.callMaterialSheets.reduce((all, sheet) => {
            return all.concat(sheet.materials);
          }, []);
        } else {
          this.callMaterialSheets = [];
          this.materialDetails = [];
          Message({
            message: response.msg || this.$t('callMaterialSheet.getMaterialDetailsFailed'),
            type: 'error'
          });
        }
      } catch (error) {
        console.error('获取叫料单预览失败', error);
        this.callMaterialSheets = [];
        this.materialDetails = [];
        Message({
          message: this.$t('callMaterialSheet.getMaterialDetailsFailed'),
          type: 'error'
        });
      }
    },
    
    // 删除行（兼容旧逻辑）
    handleDeleteRow(index) {
      this.materialDetails.splice(index, 1);
    },

    // 删除物料
    handleDeleteMaterial(sheetIndex, materialIndex) {
      this.callMaterialSheets[sheetIndex].materials.splice(materialIndex, 1);
      // 同步更新materialDetails以保持兼容性
      this.materialDetails = this.callMaterialSheets.reduce((all, sheet) => {
        return all.concat(sheet.materials);
      }, []);
    },

    // 全选/全不选
    handleSelectAllSheets(selectAll) {
      this.callMaterialSheets.forEach(sheet => {
        sheet.isSelected = selectAll;
      });
    },
    
    // 提交表单（批量提交）
    async handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const selectedSheets = this.callMaterialSheets.filter(sheet => sheet.isSelected);

          if (selectedSheets.length === 0) {
            Message({
              message: '请至少选择一个叫料单进行提交',
              type: 'warning'
            });
            return;
          }

          // 验证每个选中的叫料单是否有有效物料
          const hasValidMaterials = selectedSheets.some(sheet =>
            sheet.materials.some(material => material.ActualQty > 0)
          );

          if (!hasValidMaterials) {
            Message({
              message: '请至少为一个叫料单设置有效的物料数量',
              type: 'warning'
            });
            return;
          }

          this.submitLoading = true;

          try {
            // 构造批量提交数据，按照接口文档格式
            const batchRequests = selectedSheets
              .filter(sheet => sheet.materials.some(material => material.ActualQty > 0))
              .map(sheet => ({
                ProductionOrderId: this.form.productionOrderId,
                CallTime: this.form.RequestTime,
                LineSideWarehouse: sheet.sheetConfig.lineSideWarehouse || this.form.lineSideWarehouse,
                CallPoint: sheet.sheetConfig.callPoint || this.form.callPoint,
                RequestType: sheet.sheetConfig.requestType,
                CallMaterialType: sheet.sheetConfig.callMaterialType,
                Details: sheet.materials
                  .filter(material => material.ActualQty > 0)
                  .map(material => ({
                    MaterialId: material.MaterialId,
                    MaterialCode: material.MaterialCode,
                    MaterialName: material.MaterialName,
                    MaterialVersionId: material.MaterialVersionId,
                    MaterialVersionCode: material.MaterialVersionCode,
                    Unit: material.Unit,
                    RequestQty: material.ActualQty, // 使用实际数量作为请求数量
                    BatchNo: material.BatchNo,
                    PalletNo: material.PalletNo
                  }))
              }));

            const res = await AddCallMaterialSheetBatch(batchRequests);

            if (res.success) {
              Message({
                message: `成功创建 ${batchRequests.length} 个叫料单`,
                type: 'success'
              });
              this.dialogFormVisible = false;
              this.gettabeldata();
            } else {
              Message({
                message: res.msg || '批量创建叫料单失败',
                type: 'error'
              });
            }

          } catch (error) {
            console.error(error);
            Message({
              message: '批量提交失败',
              type: 'error'
            });
          } finally {
            this.submitLoading = false;
          }
        }
      });
    },
    
    handleDialogClose() {
      this.$refs.form.resetFields();
      this.materialDetails = [];
      this.callMaterialSheets = [];
    },
    
    // 加载产线下拉选项
    async loadLineOptions() {
      try {
        const response = await getLineList();
        this.lineOptions = response.response || [];
      } catch (error) {
        console.error('获取产线列表失败', error);
        Message({
          message: this.$t('callMaterialSheet.searchLineFailed'),
          type: 'error'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 10px;
}

.tablebox {
  height: calc(100vh - 340px);
  border-radius: 4px;
  box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.pd-left {
  padding-left: 5px;
}

.paginationbox {
  width: 100%;
  height: 10%;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: #303133;
}

.dialog-footer {
  text-align: right;
}

::v-deep .el-dialog__body {
  padding: 10px 20px;
}

.mt-8p {
  margin-top: 8px;
}

/* 叫料单预览列表样式 */
.call-material-sheets-section {
  margin-top: 20px;

  .sheets-operations {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;

    .selected-info {
      color: #606266;
      font-size: 14px;
    }
  }

  .call-sheet-block {
    border: 1px solid #EBEEF5;
    border-radius: 6px;
    margin-bottom: 15px;
    transition: all 0.3s;

    &.selected {
      border-color: #409EFF;
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
    }

    .sheet-header {
      background: #F5F7FA;
      padding: 12px 15px;
      display: flex;
      align-items: center;
      gap: 10px;
      border-bottom: 1px solid #EBEEF5;

      .sheet-title {
        font-weight: 500;
        color: #303133;
      }

      .material-count {
        color: #909399;
        font-size: 12px;
      }

      .el-button {
        margin-left: auto;
      }
    }

    .sheet-config {
      padding: 15px;
      background: #fafbfc;
      border-bottom: 1px solid #EBEEF5;
    }

    .sheet-materials {
      padding: 15px;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px;
    color: #909399;

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}
</style>